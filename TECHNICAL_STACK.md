# Technical Stack Specifications

## Overview
This document provides detailed technical specifications for the AI-powered internal chat agent system, based on a Node.js + TypeScript architecture.

## Backend Architecture

### Core Framework
- **Runtime**: Node.js (v18+ LTS)
- **Language**: TypeScript (v5.0+)
- **Web Framework**: Express.js (v4.18+)
- **API Design**: RESTful APIs + WebSocket for real-time chat

### Key Dependencies
```json
{
  "express": "^4.18.0",
  "typescript": "^5.0.0",
  "cors": "^2.8.5",
  "helmet": "^7.0.0",
  "express-rate-limit": "^6.7.0",
  "socket.io": "^4.7.0",
  "jsonwebtoken": "^9.0.0",
  "bcryptjs": "^2.4.3",
  "joi": "^17.9.0",
  "winston": "^3.8.0"
}
```

### Authentication & Security
- **OAuth2 Provider**: Integration with company identity provider
- **JWT Tokens**: For session management and API authentication
- **API Keys**: For CLI tool authentication
- **Rate Limiting**: Express rate limiting middleware
- **Security Headers**: Helmet.js for security headers
- **Input Validation**: Joi for request validation

## Database Architecture

### Primary Database
- **Platform**: Supabase (PostgreSQL 15+) or Azure Database for PostgreSQL
- **Vector Extension**: pgvector for embeddings storage
- **ORM**: Prisma (v5.0+) for type-safe database operations
- **Alternative**: Azure Cosmos DB for MongoDB API (if NoSQL preferred)

### Database Schema Design
```typescript
// Core entities
interface User {
  id: string;
  email: string;
  name: string;
  role: 'user' | 'admin' | 'manager';
  createdAt: Date;
  updatedAt: Date;
}

interface Document {
  id: string;
  title: string;
  content: string;
  filePath: string;
  mimeType: string;
  uploadedBy: string;
  createdAt: Date;
  updatedAt: Date;
}

interface ChatSession {
  id: string;
  userId: string;
  title: string;
  createdAt: Date;
  updatedAt: Date;
}

interface Message {
  id: string;
  sessionId: string;
  content: string;
  role: 'user' | 'assistant';
  confidence?: number;
  escalated: boolean;
  createdAt: Date;
}

interface EscalatedQuery {
  id: string;
  messageId: string;
  status: 'open' | 'closed';
  assignedTo?: string;
  resolution?: string;
  createdAt: Date;
  resolvedAt?: Date;
}
```

### Vector Storage
- **Embeddings Model**: OpenAI text-embedding-ada-002
- **Vector Dimensions**: 1536 (OpenAI standard)
- **Similarity Search**: Cosine similarity via pgvector
- **Indexing**: HNSW index for efficient similarity search

## Frontend Architecture

### Web Application
- **Framework**: React (v18+) with TypeScript
- **Build Tool**: Vite (v4.0+)
- **State Management**: Zustand or React Query
- **UI Framework**: Tailwind CSS + Headless UI
- **Real-time**: Socket.io-client for live chat

### Key Frontend Dependencies
```json
{
  "react": "^18.2.0",
  "react-dom": "^18.2.0",
  "typescript": "^5.0.0",
  "vite": "^4.0.0",
  "tailwindcss": "^3.3.0",
  "@headlessui/react": "^1.7.0",
  "socket.io-client": "^4.7.0",
  "react-query": "^3.39.0",
  "react-router-dom": "^6.8.0",
  "react-hook-form": "^7.43.0"
}
```

## LLM Integration

### AI/ML Stack
- **Primary LLM**: OpenAI GPT-4 or GPT-3.5-turbo
- **Alternative LLM**: Anthropic Claude (configurable)
- **Embeddings**: OpenAI text-embedding-ada-002
- **RAG Framework**: Custom implementation using LangChain.js

### LLM Dependencies
```json
{
  "openai": "^4.0.0",
  "@anthropic-ai/sdk": "^0.6.0",
  "langchain": "^0.0.140",
  "@langchain/openai": "^0.0.14",
  "@langchain/community": "^0.0.25"
}
```

### RAG Implementation
- **Document Chunking**: Recursive character text splitter
- **Chunk Size**: 1000 characters with 200 character overlap
- **Retrieval**: Top-k similarity search (k=5)
- **Context Window**: 4000 tokens for GPT-3.5, 8000 for GPT-4
- **Confidence Scoring**: Custom confidence calculation based on retrieval scores

## Document Processing

### File Processing Stack
```json
{
  "pdf-parse": "^1.1.1",
  "mammoth": "^1.5.1",
  "multer": "^1.4.5",
  "sharp": "^0.32.0",
  "file-type": "^18.5.0"
}
```

### Supported Formats
- **PDF**: pdf-parse for text extraction
- **Word Documents**: mammoth.js for .docx files
- **Text Files**: Direct processing (.txt, .md)
- **Images**: OCR capability using Tesseract.js (future enhancement)

### Processing Pipeline
1. File upload validation (type, size limits)
2. Text extraction based on file type
3. Content cleaning and preprocessing
4. Text chunking for embeddings
5. Vector generation and storage
6. Metadata indexing

## Slack Integration

### Slack Bot Framework
```json
{
  "@slack/bolt": "^3.13.0",
  "@slack/web-api": "^6.8.0"
}
```

### Bot Capabilities
- **Slash Commands**: `/ask` for direct queries
- **Direct Messages**: Full conversation support
- **Channel Integration**: Mention-based responses
- **Interactive Components**: Buttons for escalation/feedback

## Command Line Interface

### CLI Framework
- **Language**: TypeScript (same as backend for consistency)
- **Runtime**: Node.js via Docker container
- **Distribution**: Docker container with pre-built CLI

```json
{
  "commander": "^10.0.0",
  "inquirer": "^9.1.0",
  "chalk": "^5.2.0",
  "ora": "^6.1.0",
  "table": "^6.8.0",
  "axios": "^1.4.0",
  "dotenv": "^16.0.0"
}
```

### CLI Container Usage
```bash
# Pull and run CLI container
docker pull ichat/cli:latest

# Run CLI commands via container
docker run --rm -it \
  -v $(pwd):/workspace \
  -v ~/.ichat:/root/.ichat \
  ichat/cli:latest auth login

# Create alias for easier usage
alias ichat-cli='docker run --rm -it -v $(pwd):/workspace -v ~/.ichat:/root/.ichat ichat/cli:latest'

# Use CLI normally
ichat-cli docs upload document.pdf
ichat-cli chat start
```

### CLI Commands Structure
```bash
ichat-cli auth login                    # Authenticate with API key
ichat-cli auth logout                   # Clear authentication
ichat-cli docs upload <file>            # Upload document
ichat-cli docs list                     # List all documents
ichat-cli docs delete <id>              # Delete document
ichat-cli users create <email>          # Create user
ichat-cli users list                    # List users
ichat-cli chat start                    # Start interactive chat
ichat-cli queries list                  # List escalated queries
ichat-cli queries respond <id>          # Respond to escalated query
ichat-cli system status                 # System health check
ichat-cli config set <key> <value>      # Set configuration
ichat-cli config get <key>              # Get configuration
```

## API Design

### REST Endpoints
```typescript
// Authentication
POST /api/auth/login
POST /api/auth/refresh
POST /api/auth/logout

// Documents
GET /api/documents
POST /api/documents/upload
PUT /api/documents/:id
DELETE /api/documents/:id

// Chat
GET /api/chat/sessions
POST /api/chat/sessions
GET /api/chat/sessions/:id/messages
POST /api/chat/sessions/:id/messages

// Users (Admin only)
GET /api/users
POST /api/users
PUT /api/users/:id
DELETE /api/users/:id

// Escalated Queries
GET /api/queries/escalated
POST /api/queries/:id/respond
PUT /api/queries/:id/status

// System
GET /api/health
GET /api/metrics
```

### WebSocket Events
```typescript
// Client to Server
'chat:join_session'
'chat:send_message'
'chat:typing'

// Server to Client
'chat:message_received'
'chat:typing_indicator'
'chat:escalation_created'
'system:notification'
```

## Azure Cloud Infrastructure

### Hosting & Deployment
- **App Service**: Azure App Service for Node.js backend hosting
- **Static Web Apps**: Azure Static Web Apps for React frontend
- **Container Registry**: Azure Container Registry for Docker images
- **Container Instances**: Azure Container Instances for containerized deployments

### Azure Services Integration
- **Azure Database for PostgreSQL**: Managed PostgreSQL with pgvector support
- **Azure Blob Storage**: Document storage and file uploads
- **Azure Key Vault**: Secure storage for API keys and secrets
- **Azure Active Directory**: OAuth2 integration for company authentication
- **Azure Application Insights**: Application monitoring and logging
- **Azure API Management**: API gateway and rate limiting

### Storage & CDN
- **Azure Blob Storage**: Document and file storage
- **Azure CDN**: Content delivery for static assets
- **Azure Files**: Shared file storage for document processing

### Security & Compliance
- **Azure Security Center**: Security monitoring and recommendations
- **Azure Policy**: Compliance and governance enforcement
- **Azure Private Link**: Secure network connectivity
- **Azure Firewall**: Network security and access control

### Monitoring & Observability
- **Azure Monitor**: Comprehensive monitoring solution
- **Application Insights**: Application performance monitoring
- **Log Analytics**: Centralized logging and analysis
- **Azure Alerts**: Automated alerting and notifications

### CI/CD Pipeline
- **Azure DevOps**: Source control, build, and deployment pipelines
- **GitHub Actions**: Alternative CI/CD with Azure integration
- **Azure Pipelines**: Build and deployment automation

### Development & Testing
- **Azure DevTest Labs**: Development and testing environments
- **Azure Load Testing**: Performance and load testing
- **Azure Resource Manager**: Infrastructure as Code (ARM templates)

## Environment Configuration

### Development Environment
```typescript
// Azure development configuration
const azureConfig = {
  database: {
    host: process.env.AZURE_POSTGRES_HOST,
    port: 5432,
    database: process.env.AZURE_POSTGRES_DB,
    username: process.env.AZURE_POSTGRES_USER,
    password: process.env.AZURE_POSTGRES_PASSWORD,
    ssl: true
  },
  storage: {
    accountName: process.env.AZURE_STORAGE_ACCOUNT,
    accountKey: process.env.AZURE_STORAGE_KEY,
    containerName: 'documents'
  },
  keyVault: {
    vaultUrl: process.env.AZURE_KEY_VAULT_URL,
    clientId: process.env.AZURE_CLIENT_ID,
    clientSecret: process.env.AZURE_CLIENT_SECRET,
    tenantId: process.env.AZURE_TENANT_ID
  }
};
```

### Production Deployment
- **Resource Group**: Organized Azure resource management
- **App Service Plan**: Scalable hosting for backend services
- **Application Gateway**: Load balancing and SSL termination
- **Virtual Network**: Secure network isolation
- **Network Security Groups**: Traffic filtering and security rules

## Containerization

### Docker Configuration

#### Backend Dockerfile
```dockerfile
FROM node:18-alpine AS base
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS build
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM base AS runtime
COPY --from=build /app/dist ./dist
COPY --from=build /app/node_modules ./node_modules
EXPOSE 3000
CMD ["node", "dist/index.js"]
```

#### Frontend Dockerfile
```dockerfile
FROM node:18-alpine AS build
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM nginx:alpine AS runtime
COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### CLI Dockerfile
```dockerfile
FROM node:18-alpine AS build
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM node:18-alpine AS runtime
WORKDIR /app
COPY --from=build /app/dist ./dist
COPY --from=build /app/package*.json ./
RUN npm ci --only=production

# Create CLI executable
RUN npm link

# Create workspace directory for file operations
RUN mkdir -p /workspace
WORKDIR /workspace

# Set up configuration directory
RUN mkdir -p /root/.ichat

ENTRYPOINT ["ichat-cli"]
```

### CLI TypeScript Structure
```typescript
// src/cli/index.ts
import { Command } from 'commander';
import { AuthCommand } from './commands/auth';
import { DocsCommand } from './commands/docs';
import { UsersCommand } from './commands/users';
import { ChatCommand } from './commands/chat';
import { QueriesCommand } from './commands/queries';
import { SystemCommand } from './commands/system';
import { ConfigCommand } from './commands/config';

const program = new Command();

program
  .name('ichat-cli')
  .description('iChat AI Assistant CLI Tool')
  .version('1.0.0');

// Register command modules
program.addCommand(new AuthCommand().getCommand());
program.addCommand(new DocsCommand().getCommand());
program.addCommand(new UsersCommand().getCommand());
program.addCommand(new ChatCommand().getCommand());
program.addCommand(new QueriesCommand().getCommand());
program.addCommand(new SystemCommand().getCommand());
program.addCommand(new ConfigCommand().getCommand());

program.parse();
```

### CLI Configuration Management
```typescript
// src/cli/config/index.ts
import fs from 'fs';
import path from 'path';
import os from 'os';

interface CLIConfig {
  apiUrl: string;
  apiKey?: string;
  timeout: number;
  format: 'json' | 'table' | 'yaml';
}

export class ConfigManager {
  private configPath: string;
  private config: CLIConfig;

  constructor() {
    this.configPath = path.join(os.homedir(), '.ichat', 'config.json');
    this.loadConfig();
  }

  private loadConfig(): void {
    const defaultConfig: CLIConfig = {
      apiUrl: process.env.ICHAT_API_URL || 'https://api.ichat.company.com',
      timeout: 30000,
      format: 'table'
    };

    try {
      if (fs.existsSync(this.configPath)) {
        const fileConfig = JSON.parse(fs.readFileSync(this.configPath, 'utf8'));
        this.config = { ...defaultConfig, ...fileConfig };
      } else {
        this.config = defaultConfig;
        this.saveConfig();
      }
    } catch (error) {
      this.config = defaultConfig;
    }
  }

  public get(key: keyof CLIConfig): any {
    return this.config[key];
  }

  public set(key: keyof CLIConfig, value: any): void {
    this.config[key] = value;
    this.saveConfig();
  }

  private saveConfig(): void {
    const configDir = path.dirname(this.configPath);
    if (!fs.existsSync(configDir)) {
      fs.mkdirSync(configDir, { recursive: true });
    }
    fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2));
  }
}
```

### Docker Compose Development
```yaml
version: '3.8'
services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**************************************/ichat
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - db
      - redis

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "5173:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend

  db:
    image: pgvector/pgvector:pg15
    environment:
      - POSTGRES_DB=ichat
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

## Development Container

### DevContainer Configuration
```json
// .devcontainer/devcontainer.json
{
  "name": "iChat Development",
  "dockerComposeFile": ["../docker-compose.dev.yml"],
  "service": "devcontainer",
  "workspaceFolder": "/workspace",
  "features": {
    "ghcr.io/devcontainers/features/node:1": {
      "version": "18"
    },
    "ghcr.io/devcontainers/features/docker-in-docker:2": {},
    "ghcr.io/devcontainers/features/azure-cli:1": {},
    "ghcr.io/devcontainers/features/github-cli:1": {}
  },
  "customizations": {
    "vscode": {
      "extensions": [
        "ms-vscode.vscode-typescript-next",
        "bradlc.vscode-tailwindcss",
        "ms-vscode.vscode-json",
        "esbenp.prettier-vscode",
        "ms-azuretools.vscode-docker",
        "ms-vscode.azure-account",
        "ms-azuretools.vscode-azureappservice"
      ],
      "settings": {
        "typescript.preferences.includePackageJsonAutoImports": "on",
        "editor.formatOnSave": true,
        "editor.defaultFormatter": "esbenp.prettier-vscode"
      }
    }
  },
  "forwardPorts": [3000, 5173, 5432, 6379],
  "postCreateCommand": "npm install && npm run setup:dev",
  "remoteUser": "node"
}
```

### DevContainer Docker Compose
```yaml
# .devcontainer/docker-compose.yml
version: '3.8'
services:
  devcontainer:
    build:
      context: .
      dockerfile: Dockerfile.dev
    volumes:
      - ../..:/workspace:cached
      - node_modules:/workspace/node_modules
    command: sleep infinity
    environment:
      - NODE_ENV=development
    depends_on:
      - db
      - redis

  db:
    image: pgvector/pgvector:pg15
    restart: unless-stopped
    environment:
      POSTGRES_DB: ichat_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: devpassword
    volumes:
      - postgres-data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    restart: unless-stopped

volumes:
  node_modules:
  postgres-data:
```

### Development Dockerfile
```dockerfile
# .devcontainer/Dockerfile.dev
FROM node:18-bullseye

# Install additional tools
RUN apt-get update && apt-get install -y \
    git \
    curl \
    vim \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Install global npm packages
RUN npm install -g \
    typescript \
    ts-node \
    nodemon \
    prisma \
    @azure/cli

# Create non-root user
ARG USERNAME=node
ARG USER_UID=1000
ARG USER_GID=$USER_UID

RUN groupmod --gid $USER_GID $USERNAME \
    && usermod --uid $USER_UID --gid $USER_GID $USERNAME

USER $USERNAME
WORKDIR /workspace
```
